<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DRC Generator</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/docx@9.5.1/dist/index.umd.cjs"></script>
    <script src="https://unpkg.com/file-saver/dist/FileSaver.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; background-color: #f0f0f0; padding: 20px; }
        #app-container { max-width: 800px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        div, fieldset { margin-bottom: 15px; }
        label, legend { font-weight: bold; display: block; margin-bottom: 5px; }
        input[type="text"], select, textarea { width: 100%; padding: 8px; box-sizing: border-box; border: 1px solid #ccc; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background-color: #0056b3; }
        #addReferenceButton { width: 30px; height: 30px; font-size: 20px; line-height: 20px; padding: 0; }
        .reference-line { display: flex; align-items: center; margin-bottom: 5px; gap: 5px; }
        .reference-line select { width: 200px; }
        .reference-line input[type="text"] { flex-grow: 1; }
        .reference-line button { width: 30px; height: 30px; font-size: 15px; line-height: 20px; padding: 0; }
        .reference-line .remove-btn { background-color: #dc3545; }
        .reference-line .remove-btn:hover { background-color: #c82333; }
        fieldset { border: 1px solid #ccc; border-radius: 4px; padding: 10px; }
        legend { padding: 0 5px; }
    </style>
</head>
<body>
    <div id="app-container">
        <!-- Main Form -->
        <div>
            <label id="languageLabel" for="languageComboBox">Langue :</label>
            <select id="languageComboBox"><option value="ar">Arabe</option><option value="fr">Français</option></select>
        </div>
        <fieldset>
            <legend id="recipientsLegend">Destinataires</legend>
            <input type="checkbox" id="ministreCheckBox" checked> <label id="ministreLabel" for="ministreCheckBox">Ministre</label><br>
            <fieldset>
                <legend id="directionGeneraleLegend">Direction Générale</legend>
                <input type="checkbox" id="dgcerfCheckBox" checked> <label id="dgcerfLabel" for="dgcerfCheckBox">DGCERF</label><br>
                <input type="checkbox" id="dgramnCheckBox"> <label id="dgramnLabel" for="dgramnCheckBox">DGROA</label><br>
            </fieldset>
            <fieldset>
                <legend id="dcwLegend">DCW</legend>
                <input type="checkbox" id="algerCheckBox"> <label id="algerLabel" for="algerCheckBox">Alger</label><br>
                <input type="checkbox" id="boumerdesCheckBox"> <label id="boumerdesLabel" for="boumerdesCheckBox">Boumerdès</label><br>
                <input type="checkbox" id="tipazaCheckBox"> <label id="tipazaLabel" for="tipazaCheckBox">Tipaza</label><br>
            </fieldset>
        </fieldset>
        <div>
            <label id="objectLabel" for="objetTextBox">Objet :</label>
            <input type="text" id="objetTextBox">
        </div>
        <fieldset>
            <legend id="referenceLegend">Références</legend>
            <div id="referencesFlowLayoutPanel"></div>
            <button id="addReferenceButton">+</button>
        </fieldset>
        <div>
            <label id="attachmentsLabel" for="piecesJointesComboBox">Pièces jointes :</label>
            <select id="piecesJointesComboBox"></select>
        </div>
        <div>
            <label id="bodyLabel" for="bodyComboBox">Corps :</label>
            <select id="bodyComboBox"></select>
            <br>
            <select id="closingComboBox"></select>
        </div>
        <fieldset>
            <legend id="copiesLegend">Copies</legend>
            <div>
                <strong id="infoCopyLabel">Copie pour information à :</strong><br>
                <input type="checkbox" id="copieInfoMinistreCheckBox"> <label id="copieInfoMinistreLabel" for="copieInfoMinistreCheckBox">Ministre</label><br>
                <fieldset>
                    <legend id="directionGeneraleCopiesLegend">Direction Générale</legend>
                    <input type="checkbox" id="copieInfoDgcerfCheckBox"> <label id="copieInfoDgcerfLabel" for="copieInfoDgcerfCheckBox">DGCERF</label><br>
                    <input type="checkbox" id="copieInfoDgroaCheckBox"> <label id="copieInfoDgroaLabel" for="copieInfoDgroaCheckBox">DGROA</label><br>
                </fieldset>
                <input type="checkbox" id="copieInfoDirectionsRegionalesCheckBox"> <label id="copieInfoDirectionsRegionalesLabel" for="copieInfoDirectionsRegionalesCheckBox">Directions Régionales</label><br>
            </div>
            <div>
                <label id="executionCopyLabel" for="copieExecutionTextBox">Copie pour exécution à :</label>
                <input type="text" id="copieExecutionTextBox">
            </div>
        </fieldset>
        <button id="generateButton">Générer le document</button>
    </div>

    <!-- Modal Container -->
    <div id="vue-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-[1600px] shadow-lg rounded-md bg-white">
            <div id="vue-app-content"></div>
        </div>
    </div>

    <!-- Vue App Template -->
    <template id="vue-template">
        <div class="grid grid-cols-1 md:grid-cols-12 gap-8">
            <div class="bg-white p-6 rounded-lg shadow-md md:col-span-5">
                <h2 class="text-xl font-semibold mb-4 border-b pb-2">أدخل المعلومات</h2>
                <form @submit.prevent>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="sm:col-span-2">
                            <label for="directionType" class="block text-sm font-medium text-gray-700 mb-1">نوع الهيكل</label>
                            <select id="directionType" v-model="directionType" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="generale">المديرية العامة</option>
                                <option value="regionale">المديرية الجهوية</option>
                            </select>
                        </div>
                        
                        <template v-if="isGenerale">
                            <div class="sm:col-span-2">
                                <label for="directionGenerale" class="block text-sm font-medium text-gray-700 mb-1">المديرية العامة</label>
                                <select v-model="selectedDirectionGenerale" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option v-for="dg in generaleData" :key="dg.Nom" :value="dg">{{ dg.Nom }}</option>
                                </select>
                            </div>

                            <div v-if="selectedDirectionGenerale" class="sm:col-span-2">
                                <label for="direction" class="block text-sm font-medium text-gray-700 mb-1">المديرية</label>
                                <select v-model="selectedDirection" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option v-for="d in selectedDirectionGenerale.Directions" :key="d.Nom" :value="d">{{ d.Nom }}</option>
                                </select>
                            </div>

                            <div v-if="selectedDirection" class="sm:col-span-2">
                                <label for="sousDirection" class="block text-sm font-medium text-gray-700 mb-1">المديرية الفرعية</label>
                                <select v-model="selectedSousDirection" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option v-for="sd in selectedDirection.SousDirections" :key="sd.Nom" :value="sd">{{ sd.Nom }}</option>
                                </select>
                            </div>
                        </template>

                        <template v-else>
                             <div v-for="(level, index) in regionaleLevels" :key="index" class="sm:col-span-2">
                                <label :for="'level' + index" class="block text-sm font-medium text-gray-700 mb-1">{{ level.Name }}</label>
                                <select v-model="level.SelectedValue" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option value="">-- غير محدد --</option>
                                    <option v-for="(abbr, name) in level.Options" :key="abbr" :value="abbr">{{ name }}</option>
                                    <option value="custom">أخرى (إدخال)</option>
                                </select>
                                <input v-if="level.SelectedValue === 'custom'" type="text" v-model="level.CustomValue" class="mt-2 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="أدخل قيمة جديدة">
                            </div>
                        </template>

                        <div>
                             <label for="referenceId" class="block text-sm font-medium text-gray-700 mb-1">رقم التسجيل</label>
                             <input type="number" id="referenceId" v-model="referenceId" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="مثال: 1478">
                        </div>
                        <div>
                             <label for="referenceYear" class="block text-sm font-medium text-gray-700 mb-1">السنة</label>
                             <input type="number" id="referenceYear" v-model="referenceYear" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>
                    </div>
                </form>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md md:col-span-7">
                <div class="flex justify-between items-center mb-4 border-b pb-2">
                    <h2 class="text-xl font-semibold">النص المُنشأ</h2>
                </div>
                <div dir="rtl" class="text-right bg-gray-50 p-4 rounded-md border min-h-[100px]">
                    <p>{{ computedNumeroReference }}</p>
                </div>
                <div class="flex justify-end mt-6">
                    <button @click="validateAndClose" type="button" class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out mr-2">موافق</button>
                    <button @click="cancelAndClose" type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">إلغاء</button>
                </div>
            </div>
        </div>
    </template>

    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // --- Main App Logic ---
        const languageComboBox = document.getElementById('languageComboBox');
        const objetTextBox = document.getElementById('objetTextBox');
        const piecesJointesComboBox = document.getElementById('piecesJointesComboBox');
        const bodyComboBox = document.getElementById('bodyComboBox');
        const closingComboBox = document.getElementById('closingComboBox');
        const referencesFlowLayoutPanel = document.getElementById('referencesFlowLayoutPanel');
        const addReferenceButton = document.getElementById('addReferenceButton');
        const generateButton = document.getElementById('generateButton');
        const copieExecutionTextBox = document.getElementById('copieExecutionTextBox');

        const translations = {
            ar: {
                languageLabel: "اللغة:", recipientsLegend: "المرسل إليهم", ministreLabel: "الوزير", dgcerfLabel: "DGCERF", dgramnLabel: "DGRAMN",
                directionGeneraleLegend: "المديرية العامة", directionGeneraleCopiesLegend: "المديرية العامة", dcwLegend: "DCW", algerLabel: "الجزائر",
                boumerdesLabel: "بومرداس", tipazaLabel: "تيبازة", objectLabel: "الموضوع:", referenceLegend: "المرجع", attachmentsLabel: "المرفقات:",
                bodyLabel: "نص الرسالة:", copiesLegend: "النسخ", infoCopyLabel: "نسخة للإعلام إلى:", copieInfoMinistreLabel: "الوزير",
                copieInfoDgcerfLabel: "DGCERF", copieInfoDgroaLabel: "DGRAMN", copieInfoDirectionsRegionalesLabel: "المديريات الجهوية",
                executionCopyLabel: "نسخة للتنفيذ إلى:", objet: "ف/ي إعداد دراسة حول أسعار لحوم البقر الطازجة المحلية و لحوم المقايضة",
                bodyOptions: [
                    "تبعا لمراسلتكم المشار إليها في المرجع أعلاه، والمتعلقة بإعداد دراسة حول ...، يشرفني أن أعلمكم أن مصالح مديرية التجارة لولاية الجزائر قد أجرت تحقيقا في هذا الشأن، والذي أفضى إلى النتائج التالية:",
                    "تبعا لمراسلة مديرية التجارة لولاية تيبازة المذكورة في المرجع أعلاه، و المتضمنة نتائج التجارب المجراة على عينة من منتوج ...، حيث أثبتت نتائج تجارب ... عدم مطابقته.",
                    "إضافة إلى مراسلتنا المشار إليها في المرجع أعلاه، و المتعلقة بنتائج التحقيق المفتوح حول...، يشرفني أن أوافيكم بجدول يتضمن نتائج العمل الذي أنجزته مصالح الرقابة التابعة لمديرية التجارة لولاية الجزائر.",
                    "عطفا على مراسلتنا المشار إليها في المرجع أعلاه، و المتعلقة بنتائج التحقيق المفتوح حول...، يشرفني أن أوافيكم بجدول يتضمن نتائج العمل المنجز من قبل مصالح الرقابة التابعة لمديرية التجارة لولاية الجزائر.",
                    "ردا على مراسلتكم المشار إليها في المرجع أعلاه و المتعلقة بـ...، يشرفني أن أعلمكم أن مصالح مديرية التجارة لولاية الجزائر قد فتحت تحقيقا في هذا الشأن، حيث..."
                ],
                closingOptions: [
                    "و عليه، نطلب منكم اتخاذ الإجراءات القانونية اللازمة، مع موافاتنا بنتائج تدخلاتكم في حينها عبر البريد الرسمي و البريد الإلكتروني على العنوان التالي: <EMAIL>"
                ],
                piecesJointes: ["جدول", "جدولان", "جداول"], referenceTypes: ["مراسلتكم رقم", "مراسلتنا رقم", "المراسلة الوزارية رقم"],
                referenceDateLabel: "المؤرخة في", generateButton: "إنشاء المستند"
            },
            fr: {
                languageLabel: "Langue :", recipientsLegend: "Destinataires", ministreLabel: "Ministre", dgcerfLabel: "DGCERF", dgramnLabel: "DGRAMN",
                directionGeneraleLegend: "Direction Générale", directionGeneraleCopiesLegend: "Direction Générale", dcwLegend: "DCW", algerLabel: "Alger",
                boumerdesLabel: "Boumerdès", tipazaLabel: "Tipaza", objectLabel: "Objet :", referenceLegend: "Référence", attachmentsLabel: "Pièces jointes :",
                bodyLabel: "Corps de la lettre :", copiesLegend: "Copies", infoCopyLabel: "Copie pour information à :", copieInfoMinistreLabel: "Ministre",
                copieInfoDgcerfLabel: "DGCERF", copieInfoDgroaLabel: "DGRAMN", copieInfoDirectionsRegionalesLabel: "Directions Régionales",
                executionCopyLabel: "Copie pour exécution à :", objet: "A/S Élaboration d'une étude sur les prix de la viande bovine fraîche locale et de la viande de troc",
                bodyOptions: [
                    "Suite à votre envoi référencé ci-dessus, concernant la réalisation d'une étude sur ..., j'ai l'honneur de vous informer que les services de la Direction du Commerce de la Wilaya d'Alger ont mené une enquête à ce sujet, qui a abouti aux résultats suivants :",
                    "Suite à l'envoi de la Direction du Commerce de la Wilaya de Tipaza, mentionné en référence ci-dessus, et contenant les résultats des tests effectués sur un échantillon du produit..., les résultats des tests de... ont prouvé sa non-conformité.",
                    "En complément à notre envoi référencé ci-dessus, et relatif aux résultats de l'enquête ouverte sur..., j'ai l'honneur de vous faire parvenir un tableau contenant les résultats du travail accompli par les services de contrôle de la Direction du Commerce de la Wilaya d'Alger.",
                    "Suite à notre envoi référencé ci-dessus, et relatif aux résultats de l'enquête ouverte sur..., j'ai l'honneur de vous faire parvenir un tableau contenant les résultats du travail réalisé par les services de contrôle de la Direction du Commerce de la Wilaya d'Alger.",
                    "En réponse à votre envoi référencé ci-dessus et relatif à..., j'ai l'honneur de vous informer que les services de la Direction du Commerce de la Wilaya d'Alger ont ouvert une enquête à ce sujet, où..."
                ],
                closingOptions: [
                    "Par conséquent, nous vous demandons de prendre les mesures juridiques nécessaires et de nous informer des résultats de vos interventions en temps voulu par courrier officiel et par courrier électronique à l'adresse suivante : <EMAIL>"
                ],
                piecesJointes: ["Tableau", "Tableaux"], referenceTypes: ["Votre envoi n°", "Notre envoi n°", "Envoi ministériel n°"],
                referenceDateLabel: "en date du", generateButton: "Générer le document"
            }
        };
        
        function updateUI(language) {
            const dir = language === 'ar' ? 'rtl' : 'ltr';
            document.documentElement.dir = dir;
            const trans = translations[language];
            Object.keys(trans).forEach(key => {
                const el = document.getElementById(key);
                if (el) {
                    if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA') el.value = trans[key];
                    else el.textContent = trans[key];
                }
            });
            objetTextBox.value = trans.objet;
            bodyComboBox.innerHTML = '';
            trans.bodyOptions.forEach(item => {
                const option = document.createElement('option');
                option.value = item;
                option.textContent = item.substring(0, 100) + '...'; // Show a preview
                bodyComboBox.appendChild(option);
            });
            closingComboBox.innerHTML = '';
            trans.closingOptions.forEach(item => {
                const option = document.createElement('option');
                option.value = item;
                option.textContent = item.substring(0, 100) + '...'; // Show a preview
                closingComboBox.appendChild(option);
            });
            document.querySelectorAll('input, textarea, select').forEach(el => {
                el.style.direction = dir;
                el.style.textAlign = dir === 'rtl' ? 'right' : 'left';
            });
            piecesJointesComboBox.innerHTML = '';
            trans.piecesJointes.forEach(item => {
                const option = document.createElement('option');
                option.value = item; option.textContent = item;
                piecesJointesComboBox.appendChild(option);
            });
            referencesFlowLayoutPanel.innerHTML = '';
            addReferenceLine();
        }

        function addReferenceLine() {
            const language = languageComboBox.value;
            const panel = document.createElement('div');
            panel.className = 'reference-line';

            const comboBox = document.createElement('select');
            translations[language].referenceTypes.forEach(item => {
                const option = document.createElement('option');
                option.value = item; option.textContent = item;
                comboBox.appendChild(option);
            });

            const textBox = document.createElement('input');
            textBox.type = 'text';

            const dateLabel = document.createElement('label');
            dateLabel.textContent = translations[language].referenceDateLabel;
            dateLabel.style.whiteSpace = 'nowrap';

            const dateInput = document.createElement('input');
            dateInput.type = 'date';
            dateInput.style.width = '150px';
            dateInput.value = new Date().toISOString().split('T')[0];

            const removeButton = document.createElement('button');
            removeButton.textContent = 'X';
            removeButton.className = 'remove-btn';
            removeButton.onclick = () => panel.remove();

            const genButton = document.createElement('button');
            genButton.textContent = '...';
            genButton.type = 'button';
            genButton.onclick = () => openReferenceModal(textBox);
            
            panel.append(comboBox, textBox, genButton, dateLabel, dateInput, removeButton);
            referencesFlowLayoutPanel.appendChild(panel);
        }

        function getPreposition(word) {
            if (!word) return "";
            const vowels = ['a', 'e', 'i', 'o', 'u', 'y'];
            const firstLetter = word.trim().substring(0, 1).toLowerCase();
            return vowels.includes(firstLetter) ? "d'" : "de ";
        }

        function formatWilayaListWithPrepositions(wilayas) {
            if (!wilayas || wilayas.length === 0) return "";
            const formattedWilayas = wilayas.map(w => getPreposition(w) + w);
            if (formattedWilayas.length === 1) return formattedWilayas;
            return formattedWilayas.slice(0, -1).join(', ') + ' et ' + formattedWilayas.slice(-1);
        }

        function formatArabicList(items) {
            if (!items || items.length === 0) return "";
            if (items.length === 1) return items;
            return items.slice(0, -1).join('، ') + ' و ' + items.slice(-1);
        }

        // --- Document Generation Logic ---
        function generateDocument() {
            const { Document, Packer, Paragraph, TextRun, AlignmentType, Tab, TabStopType, TabStopPosition } = docx;

            const language = languageComboBox.value;
            const isArabic = language === 'ar';
            const year = new Date().getFullYear();

            const children = [];

            const createBidiParagraph = (text, alignment, bold = false) => new Paragraph({
                alignment: alignment,
                bidirectional: isArabic,
                children: [new TextRun({ text: text, rightToLeft: isArabic, bold: bold })]
            });
            
            if (isArabic) {
                children.push(new Paragraph({
                    alignment: AlignmentType.CENTER,
                    bidirectional: true,
                    children: [new TextRun({ text: "الجمهورية الجزائرية الديمقراطية الشعبية", rightToLeft: true, bold: true, size: 28 })]
                }));
                children.push(new Paragraph(""));
                children.push(new Paragraph({
                    alignment: AlignmentType.LEFT,
                    bidirectional: true,
                    children: [new TextRun({ text: "وزارة التجارة الداخلية و ضبط السوق الوطنية", rightToLeft: true, bold: true, size: 26 })]
                }));
                children.push(new Paragraph({
                    alignment: AlignmentType.LEFT,
                    bidirectional: true,
                    children: [new TextRun({ text: "المديرية الجهوية للتجارة بالجزائر", rightToLeft: true, bold: true, size: 26 })]
                }));
                children.push(new Paragraph({
                    alignment: AlignmentType.LEFT,
                    bidirectional: true,
                    children: [new TextRun({ text: "مصلحة تخطيط و متابعة المراقبة و تقييمها", rightToLeft: true, bold: true, size: 26 })]
                }));
                children.push(new Paragraph({
                    alignment: AlignmentType.LEFT,
                    bidirectional: true,
                    children: [new TextRun({ text: "مكتب التحقيقات المتخصصة", rightToLeft: true, bold: true, size: 26 })]
                }));
                children.push(new Paragraph({
                    bidirectional: true,
                    children: [
                       new TextRun({ text: "الرقم:", bold: true, rightToLeft: true, size: 26 }),
                       new TextRun({ text: `                /و ت/م ج ت ج/م ت م م ت/م ت خ/${year}` + ' '.repeat(23) + "الجزائر، في", rightToLeft: true, size: 26 })
                   ]
                }));
            } else {
                children.push(new Paragraph({
                    alignment: AlignmentType.CENTER,
                    children: [new TextRun({ text: "République Algérienne Démocratique et Populaire", bold: true, size: 28 })]
                }));
                children.push(new Paragraph(""));
                children.push(new Paragraph({
                    alignment: AlignmentType.LEFT,
                    children: [new TextRun({ text: "Ministère du Commerce Intérieur et de la Régulation du Marché National", bold: true, size: 26 })]
                }));
                children.push(new Paragraph({
                    alignment: AlignmentType.LEFT,
                    children: [new TextRun({ text: "Direction Régionale du Commerce d'Alger", bold: true, size: 26 })]
                }));
                children.push(new Paragraph({
                    alignment: AlignmentType.LEFT,
                    children: [new TextRun({ text: "Service de la Planification, du Suivi et de l'Évaluation du Contrôle", bold: true, size: 26 })]
                }));
                children.push(new Paragraph({
                    alignment: AlignmentType.LEFT,
                    children: [new TextRun({ text: "Bureau des Enquêtes Spécialisées", bold: true, size: 26 })]
                }));
                children.push(new Paragraph({
                     children: [
                        new TextRun({ text: "Nº :", bold: true, size: 26 }),
                        new TextRun({text: `                /MC/DRCA/SPSEC/BES/${year}` + ' '.repeat(23) + 'Alger, le', size: 26})
                    ]
                }));
            }

            children.push(new Paragraph({ text: ""}));

            const ministre = document.getElementById('ministreCheckBox').checked;
            const dgcerf = document.getElementById('dgcerfCheckBox').checked;
            const dgramn = document.getElementById('dgramnCheckBox').checked;
            const alger = document.getElementById('algerCheckBox').checked;
            const boumerdes = document.getElementById('boumerdesCheckBox').checked;
            const tipaza = document.getElementById('tipazaCheckBox').checked;
            const wilayas = [];
            if (alger) wilayas.push('Alger'); if (boumerdes) wilayas.push('Boumerdès'); if (tipaza) wilayas.push('Tipaza');

            if (ministre || dgcerf || dgramn || wilayas.length > 0) {
                 children.push(createBidiParagraph(isArabic ? 'إلى' : 'À', AlignmentType.CENTER, true));
                 if(ministre) children.push(createBidiParagraph(isArabic ? 'السيد وزير التجارة الداخلية و ضبط السوق الوطنية' : 'Monsieur le Ministre du Commerce Intérieur et de la Régulation du Marché National', AlignmentType.CENTER, true));
                 if(dgcerf) children.push(createBidiParagraph(isArabic ? 'المديرية العامة للرقابة الاقتصادية و قمع الغش' : 'Direction Générale du Contrôle Économique et de la Répression des Fraudes', AlignmentType.CENTER, true));
                 if(dgramn) children.push(createBidiParagraph(isArabic ? 'المديرية العامة لضبط و تموين السوق الوطنية' : "Direction Générale de la Régulation et de l'Approvisionnement du Marché National", AlignmentType.CENTER, true));
                 
                 if (!isArabic) {
                    if (wilayas.length === 1) children.push(createBidiParagraph(`Monsieur le Directeur du Commerce de la Wilaya ${getPreposition(wilayas[0])}${wilayas[0]}`, AlignmentType.CENTER, true));
                    else if (wilayas.length > 1) {
                        children.push(createBidiParagraph(`Messieurs les Directeurs du Commerce`, AlignmentType.CENTER, true));
                        children.push(createBidiParagraph(`des Wilayas ${formatWilayaListWithPrepositions(wilayas)}`, AlignmentType.CENTER, true));
                    }
                 } else {
                    const wilayasAr = [];
                    if (alger) wilayasAr.push('الجزائر'); if (boumerdes) wilayasAr.push('بومرداس'); if (tipaza) wilayasAr.push('تيبازة');
                    if (wilayasAr.length === 1) children.push(createBidiParagraph(`السيد مدير التجارة لولاية ${wilayasAr[0]}`, AlignmentType.CENTER, true));
                    else if (wilayasAr.length > 1) {
                        const title = wilayasAr.length === 2 ? "السيدين مديري التجارة" : "السادة مدراء التجارة";
                        const prefix = wilayasAr.length === 2 ? "لولايتي" : "لولايات";
                        children.push(createBidiParagraph(title, AlignmentType.CENTER, true));
                        children.push(createBidiParagraph(`${prefix} ${formatArabicList(wilayasAr)}`, AlignmentType.CENTER, true));
                    }
                 }
            }

            children.push(new Paragraph(""));

            const detailsAlign = isArabic ? AlignmentType.LEFT : AlignmentType.LEFT;
            children.push(new Paragraph({
                alignment: detailsAlign,
                bidirectional: isArabic,
                children: [
                    new TextRun({ text: `${isArabic ? 'الموضوع:' : 'Objet :'}`, bold: true, rightToLeft: isArabic }),
                    new TextRun({ text: ` ${objetTextBox.value}`, rightToLeft: isArabic })
                ]
            }));
            
            const references = Array.from(document.querySelectorAll('.reference-line')).map(line => {
                const combo = line.querySelector('select');
                const text = line.querySelector('input[type="text"]');
                const date = line.querySelector('input[type="date"]');
                let refText = (combo && text && text.value) ? `${combo.value} ${text.value}` : null;
                if (refText && date && date.value) {
                    const dateObj = new Date(date.value);
                    const day = String(dateObj.getDate()).padStart(2, '0');
                    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                    const year = dateObj.getFullYear();
                    const formattedDate = `${day}/${month}/${year}`;
                    refText += ` ${translations[language].referenceDateLabel} ${formattedDate}`;
                }
                return refText;
            }).filter(Boolean);

            if (references.length > 0) {
                const label = isArabic ? 'المرجع:' : 'Référence :';
                const padding = ' '.repeat(label.length + 1);

                const referenceRuns = [];
                references.forEach((ref, i) => {
                    if (i > 0) {
                        referenceRuns.push(new TextRun({ text: padding, break: 1, rightToLeft: isArabic }));
                    }
                    referenceRuns.push(new TextRun({ text: `- ${ref.trim()}`, rightToLeft: isArabic }));
                });

                children.push(new Paragraph({
                    alignment: detailsAlign,
                    bidirectional: isArabic,
                    children: [
                        new TextRun({ text: `${label} `, bold: true, rightToLeft: isArabic }),
                        ...referenceRuns
                    ]
                }));
            }
            
            if (piecesJointesComboBox.value) {
                 children.push(new Paragraph({
                    alignment: detailsAlign,
                    bidirectional: isArabic,
                    children: [
                        new TextRun({ text: `${isArabic ? 'المرفقات:' : 'Pièces jointes :'}`, bold: true, rightToLeft: isArabic }),
                        new TextRun({ text: ` ${piecesJointesComboBox.value}`, rightToLeft: isArabic })
                    ]
                }));
            }

            children.push(new Paragraph(""));

            const bodyAlign = isArabic ? AlignmentType.LEFT : AlignmentType.JUSTIFIED;
            const bodyText = bodyComboBox.value;
            const closingText = closingComboBox.value;
            const fullBodyText = bodyText + (closingText ? '\r\n\r\n' + closingText : '');
            const bodyParts = fullBodyText.split(/\r?\n\r?\n/);
            const bodyRuns = [];
            bodyParts.forEach((part, index) => {
                bodyRuns.push(new TextRun({ text: part, rightToLeft: isArabic }));
                if (index < bodyParts.length - 1) {
                    bodyRuns.push(new TextRun({ break: 2 }));
                }
            });
            children.push(new Paragraph({
                alignment: bodyAlign,
                bidirectional: isArabic,
                children: bodyRuns
            }));
            children.push(new Paragraph(""));

            children.push(createBidiParagraph(isArabic ? 'فائق الاحترام و التقدير' : "Veuillez agréer, Messieurs, l'expression de notre haute considération.", isArabic ? AlignmentType.CENTER : AlignmentType.LEFT));
            children.push(new Paragraph(""));

            const copieInfoMinistre = document.getElementById('copieInfoMinistreCheckBox').checked;
            const copieInfoDgcerf = document.getElementById('copieInfoDgcerfCheckBox').checked;
            const copieInfoDgroa = document.getElementById('copieInfoDgroaCheckBox').checked;
            const copieInfoDirectionsRegionales = document.getElementById('copieInfoDirectionsRegionalesCheckBox').checked;
            const copieExec = copieExecutionTextBox.value;
            
            const copiesInfo = [];
            if(copieInfoMinistre) copiesInfo.push(isArabic ? 'السيد وزير التجارة الداخلية و ضبط السوق الوطنية' : 'Monsieur le Ministre du Commerce Intérieur et de la Régulation du Marché National');
            if(copieInfoDgcerf) copiesInfo.push(isArabic ? 'المديرية العامة للرقابة الاقتصادية و قمع الغش' : 'Direction Générale du Contrôle Économique et de la Répression des Fraudes');
            if(copieInfoDgroa) copiesInfo.push(isArabic ? 'المديرية العامة لضبط و تموين السوق الوطنية' : "Direction Générale de la Régulation et de l'Approvisionnement du Marché National");
            if(copieInfoDirectionsRegionales) copiesInfo.push(isArabic ? 'السادة المدراء الجهويين للتجارة بـ: سطيف، عنابة، ورقلة، باتنة، البليدة، وهران، بشار و سعيدة' : 'Messieurs les Directeurs Régionaux du Commerce de : Sétif, Annaba, Ouargla, Batna, Blida, Oran, Béchar et Saïda');

            if (copiesInfo.length > 0) {
                children.push(createBidiParagraph(isArabic ? 'نسخة للإعلام إلى:' : 'Copie pour information à :', detailsAlign, true));
                copiesInfo.forEach(copy => {
                    children.push(new Paragraph({
                        alignment: detailsAlign,
                        bidirectional: isArabic,
                        children: [new TextRun({ text: `- ${copy}`, rightToLeft: isArabic })]
                    }));
                });
            }
            if (copieExec) {
                children.push(new Paragraph({
                    alignment: detailsAlign,
                    bidirectional: isArabic,
                    children: [
                        new TextRun({ text: `${isArabic ? 'نسخة للتنفيذ إلى:' : 'Copie pour exécution à :'}`, bold: true, rightToLeft: isArabic }),
                        new TextRun({ text: ` ${copieExec}`, rightToLeft: isArabic })
                    ]
                }));
            }

            const doc = new Document({
                styles: {
                    default: {
                        document: {
                            run: {
                                font: "Calibri Light",
                                size: 26,
                            },
                        },
                    },
                },
                sections: [{
                    properties: {
                        page: {
                            margin: {
                                top: 720,
                                right: 1440,
                                bottom: 1440,
                                left: 1440,
                            },
                        },
                    },
                    children: children,
                }],
            });

            Packer.toBlob(doc).then(blob => {
                saveAs(blob, "Document.docx");
                console.log("Document généré avec succès !");
            });
        }

        // --- Modal & Vue App Logic ---
        let vueModalApp = null;

        function openReferenceModal(targetTextBox) {
            const modal = document.getElementById('vue-modal');
            modal.style.display = 'block';

            if (vueModalApp) {
                vueModalApp.unmount();
            }

            vueModalApp = Vue.createApp({
                setup() {
                    const { ref, reactive, computed, watch, onMounted } = Vue;

                    const directionType = ref('generale');
                    const referenceId = ref('');
                    const referenceYear = ref(new Date().getFullYear());
                    
                    const generaleData = reactive([]);
                    const regionaleLevels = reactive([]);

                    const selectedDirectionGenerale = ref(null);
                    const selectedDirection = ref(null);
                    const selectedSousDirection = ref(null);

                    const directions = computed(() => selectedDirectionGenerale.value?.Directions || []);
                    const sousDirections = computed(() => selectedDirection.value?.SousDirections || []);
                    
                    const isGenerale = computed(() => directionType.value === 'generale');

                    const computedNumeroReference = computed(() => {
                        const parts = [referenceId.value || '....'];
                        if (isGenerale.value) {
                            parts.push("و ت د ض س و");
                            if (selectedDirectionGenerale.value) parts.push(selectedDirectionGenerale.value.Abreviation);
                            if (selectedDirection.value) parts.push(selectedDirection.value.Abreviation);
                            if (selectedSousDirection.value) parts.push(selectedSousDirection.value.Abreviation);
                        } else {
                            parts.push("و ت");
                            regionaleLevels.forEach(level => {
                                if (level.SelectedValue === 'custom') {
                                    parts.push(level.CustomValue);
                                } else if (level.SelectedValue) {
                                    parts.push(level.SelectedValue);
                                }
                            });
                        }
                        parts.push(referenceYear.value || '....');
                        return parts.filter(p => p).join('/');
                    });
                    
                    const closeModal = () => {
                        modal.style.display = 'none';
                        if (vueModalApp) {
                            vueModalApp.unmount();
                            vueModalApp = null;
                        }
                    };
                    
                    const validateAndClose = () => {
                        targetTextBox.value = computedNumeroReference.value;
                        closeModal();
                    };

                    watch(directionType, () => {
                        selectedDirectionGenerale.value = generaleData || null;
                    });
                    
                    watch(selectedDirectionGenerale, (newVal) => {
                       selectedDirection.value = newVal?.Directions || null;
                    });

                    watch(selectedDirection, (newVal) => {
                        selectedSousDirection.value = newVal?.SousDirections || null;
                    });
                    
                    onMounted(() => {
                        const data = [
                          {
                            "Nom": "المديرية العامة لضبط و تموين السوق الوطنية", "Abreviation": "م ع ض ت س و",
                            "Directions": [
                              {
                                "Nom": "مديرية ضبط السوق الوطنية", "Abreviation": "م ض س و",
                                "SousDirections": [
                                  { "Nom": "المديرية الفرعية للمنافسة", "Abreviation": "م ف م" },
                                  { "Nom": "المديرية الفرعية لمتابعة أنظمة التعويض", "Abreviation": "م ف م أ ت" },
                                  { "Nom": "المديرية الفرعية لملاحظة السوق الوطنية", "Abreviation": "م ف م س و" }
                                ]
                              },
                              {
                                "Nom": "مديرية متابعة التموين و التوزيع", "Abreviation": "م م ت ت",
                                "SousDirections": [
                                  { "Nom": "المديرية الفرعية لمتابعة تموين السوق الوطنية", "Abreviation": "م ف م ت س و" },
                                  { "Nom": "المديرية الفرعية لمتابعة التوزيع", "Abreviation": "م ف م ت" },
                                  { "Nom": "المديرية الفرعية لمتابعة مخزونات المنتوجات الاستهلاكية", "Abreviation": "م ف م م م إ" }
                                ]
                              },
                              {
                                "Nom": "مديرية تنظيم الأنشطة الاقتصادية الخاضعة للتسجيل في السجل التجاري", "Abreviation": "م ت أ إ خ ت س ت",
                                "SousDirections": [
                                  { "Nom": "المديرية الفرعية لتأطير الأنشطة الاقتصادية الخاضعة للتسجيل في السجل التجاري", "Abreviation": "م ف ت أ إ خ ت س ت" },
                                  { "Nom": "المديرية الفرعية لتنشيط التجارة الداخلية", "Abreviation": "م ف ت ت د" },
                                  { "Nom": "المديرية الفرعية لترقية التجارة الإلكترونية", "Abreviation": "م ف ت ت إ" }
                                ]
                              },
                              {
                                "Nom": "مديرية الجودة و حماية المستهلك", "Abreviation": "م ج ح م",
                                "SousDirections": [
                                  { "Nom": "المديرية الفرعية لأمن المنتوجات الغذائية", "Abreviation": "م ف أ م غ" },
                                  { "Nom": "المديرية الفرعية لأمن المنتوجات غير الغذائية", "Abreviation": "م ف أ م غ غ" },
                                  { "Nom": "المديرية الفرعية لحماية المستهلك في مجال الخدمات", "Abreviation": "م ف ح م م خ" },
                                  { "Nom": "المديرية الفرعية للتحسيس و العلاقات مع جمعيات حماية المستهلك", "Abreviation": "م ف ت ع ج ح م" }
                                ]
                              }
                            ]
                          },
                          {
                            "Nom": "المديرية العامة للرقابة الاقتصادية و قمع الغش", "Abreviation": "م ع ر إ ق غ",
                            "Directions": [
                              {
                                "Nom": "مديرية مراقبة مطابقة المنتوجات و الخدمات و قمع الغش", "Abreviation": "م م م م خ ق غ",
                                "SousDirections": [
                                  { "Nom": "المديرية الفرعية لمراقبة المنتوجات الغذائية", "Abreviation": "م ف م م غ" },
                                  { "Nom": "المديرية الفرعية لمراقبة المنتوجات غير الغذائية و الخدمات", "Abreviation": "م ف م م غ غ خ" },
                                  { "Nom": "المديرية الفرعية للمراقبة الحدودية", "Abreviation": "م ف م ح" },
                                  { "Nom": "المديرية الفرعية لمراقبة التجارة الإلكترونية", "Abreviation": "م ف م ت إ" }
                                ]
                              },
                              {
                                "Nom": "مديرية مراقبة الممارسات التجارية و التحقيقات الاقتصادية", "Abreviation": "م م م ت ت إ",
                                "SousDirections": [
                                  { "Nom": "المديرية الفرعية لمراقبة الممارسات التجارية", "Abreviation": "م ف م م ت" },
                                  { "Nom": "المديرية الفرعية لمراقبة الممارسات المنافية للمنافسة و التحقيقات الاقتصادية", "Abreviation": "م ف م م م م ت إ" }
                                ]
                              },
                              {
                                "Nom": "مديرية تطوير و متابعة المخابر", "Abreviation": "م ت م م",
                                "SousDirections": [
                                  { "Nom": "المديرية الفرعية لتطوير نشاطات المخابر و متابعتها", "Abreviation": "م ف ت ن م م" },
                                  { "Nom": "المديرية الفرعية للإجراءات و الطرق الرسمية للتحاليل و التجارب", "Abreviation": "م ف إ ط ر ت ت" }
                                ]
                              },
                              {
                                "Nom": "مديرية التحقيقات الخصوصية و تقييم الرقابة", "Abreviation": "م ت خ ت ر",
                                "SousDirections": [
                                  { "Nom": "المديرية الفرعية للتحقيقات الخصوصية", "Abreviation": "م ف ت خ" },
                                  { "Nom": "المديرية الفرعية لتقييم أنشطة الرقابة", "Abreviation": "م ف ت أ ر" },
                                  { "Nom": "المديرية الفرعية لتحليل و متابعة الإجراءات المتعلقة بالرقابة", "Abreviation": "م ف ت م إ م ر" }
                                ]
                              }
                            ]
                          }
                        ];
                        Object.assign(generaleData, data);
                        selectedDirectionGenerale.value = generaleData || null;

                        const regionaleData = [
                            { Name: "المديرية الجهوية", Options: { "المديرية الجهوية للتجارة بالجزائر": "م ج ت ج" }, SelectedValue: "", CustomValue: "" },
                            { Name: "المصلحة", Options: { "مصلحة تخطيط و متابعة المراقبة و تقييمها": "م ت م م ت" }, SelectedValue: "", CustomValue: "" },
                            { Name: "المكتب", Options: { "مكتب التحقيقات المتخصصة": "م ت خ" }, SelectedValue: "", CustomValue: "" }
                        ];
                        Object.assign(regionaleLevels, regionaleData);
                    });

                    return { directionType, referenceId, referenceYear, generaleData, regionaleLevels, selectedDirectionGenerale, selectedDirection, selectedSousDirection, directions, sousDirections, isGenerale, computedNumeroReference, validateAndClose, cancelAndClose: closeModal };
                }
            });
            
            const template = document.getElementById('vue-template').innerHTML;
            document.getElementById('vue-app-content').innerHTML = template;
            vueModalApp.mount('#vue-app-content');
        }

        // Initial setup
        languageComboBox.addEventListener('change', (e) => updateUI(e.target.value));
        addReferenceButton.addEventListener('click', addReferenceLine);
        generateButton.addEventListener('click', generateDocument);
        updateUI(languageComboBox.value);
    });
    </script>
</body>
</html>